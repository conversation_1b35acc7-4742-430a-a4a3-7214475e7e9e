import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAppState } from "@/hooks/useAppState";
import emitter from "@/lib/eventEmitter";
import { Menu, Settings2 } from "lucide-react";
import React, { useState, useEffect } from "react";

// Model options for different conversation types
const VOICE_TO_VOICE_MODELS = ["Nova Sonic"];
const TEXT_VOICE_MODELS = ["Nova Pro", "Nova Lite", "Nova Micro", "Claude Sonnet 3.7"];

const Navbar: React.FC = () => {
  const { conversationType } = useAppState();

  const handleSidebarToggle = () => {
    emitter.emit("toggleSidebar");
  };

  const handleSettingsToggle = () => {
    emitter.emit("toggleSettings");
  };

  // Get available models based on conversation type
  const getAvailableModels = () => {
    if (conversationType === "voice-to-voice") {
      return VOICE_TO_VOICE_MODELS;
    } else if (conversationType === "text-voice") {
      return TEXT_VOICE_MODELS;
    }
    return [];
  };

  // Get default model for conversation type
  const getDefaultModel = () => {
    if (conversationType === "voice-to-voice") {
      return VOICE_TO_VOICE_MODELS[0];
    } else if (conversationType === "text-voice") {
      return TEXT_VOICE_MODELS[0];
    }
    return TEXT_VOICE_MODELS[0];
  };

  const [selectedModel, setSelectedModel] = useState(getDefaultModel());

  // Update selected model when conversation type changes
  useEffect(() => {
    const defaultModel = getDefaultModel();
    setSelectedModel(defaultModel);
    // Emit the model change immediately when conversation type changes
    if (defaultModel) {
      emitter.emit("changeLlmModel", defaultModel);
    }
  }, [conversationType]);

  const availableModels = getAvailableModels();

  return (
    <div className="bg-background flex items-center justify-between p-4 sticky top-0 z-10">
      {/* Sidebar Toggle Button */}
      <button
        className="p-2 rounded-md hover:bg-secondary focus:outline-none lg:hidden"
        onClick={handleSidebarToggle}
      >
        <Menu className="w-6 h-6" />
      </button>

      {!!conversationType && availableModels.length > 0 && (
        <Select
          disabled={false}
          value={selectedModel}
          onValueChange={(v) => {
            setSelectedModel(v);
            // Emit the model change immediately when user selects a different model
            emitter.emit("changeLlmModel", v);
          }}
        >
          <SelectTrigger className="text-base font-semibold max-w-fit rounded-full border-background shadow-none text-muted  transition-all">
            <SelectValue>{selectedModel}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {availableModels.map((model) => (
              <SelectItem key={model} value={model}>
                {model}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      {/* Settings Icon */}
      {!!conversationType && (
        <Button
          variant="outline"
          className="rounded-full ms-auto gap-1"
          onClick={handleSettingsToggle}
        >
          <Settings2 size={16} />
          Settings
        </Button>
      )}
    </div>
  );
};

export default Navbar;
