import ChatControls from "@/components/ChatControls";
import ChatMessages from "@/components/ChatMessages";
import DeleteConversationModal from "@/components/DeleteConversationModal";
import Settings from "@/components/Settings";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { VoiceIndicator } from "@/components/VoiceIndicator";
import { WebRTCVoiceChat } from "@/components/WebRTCVoiceChat";
import { useAppState } from "@/hooks/useAppState";
import { useConversation } from "@/hooks/useConversation";
import emitter from "@/lib/eventEmitter";
import { RTVIClient, Participant } from "@pipecat-ai/client-js";
import { RTVIClientAudio, RTVIClientProvider } from "@pipecat-ai/client-react";
import { SmallWebRTCTransport } from "@pipecat-ai/small-webrtc-transport";
import { DailyTransport } from "@pipecat-ai/daily-transport";
import {
  ArrowDownIcon,
  AudioWaveformIcon,
  DatabaseIcon,
  LoaderCircleIcon,
  XCircleIcon,
} from "lucide-react";
import { useEffect, useLayoutEffect, useState } from "react";
import PipecatLogo from "./svg/Pipecat";

const defaultRequestData = {
  bot_profile: "vision",
};

export function ClientPage() {
  const {
    conversationId,
    conversationType,
    setConversationType,
    dailyEnabled,
    webrtcEnabled,
  } = useAppState();

  const { conversation, isFetching } = useConversation(conversationId);
  const messages = conversation?.messages ?? [];
  const visibleMessages = messages.filter((m) => m.content.role !== "system");

  const [showMessage, setShowMessages] = useState(false);
  useEffect(() => {
    const handleShowChatMessages = () => setShowMessages(true);
    emitter.on("showChatMessages", handleShowChatMessages);
    return () => {
      emitter.off("showChatMessages", handleShowChatMessages);
    };
  }, []);
  useEffect(() => {
    if (!conversationType) {
      setShowMessages(false);
    }
  }, [conversationType]);

  const [client, setClient] = useState<RTVIClient>();

  let bot = "bedrock"
  if (conversationType === "voice-to-voice")
  {
    bot = "nova-sonic"
  }

  useEffect(() => {
    if (!conversationType) {
      setClient((prevClient) => {
        if (prevClient?.connected) prevClient?.disconnect();
        return undefined;
      });
      return;
    }

    let transport;
    if (conversationType === "voice-to-voice" || conversationType === "text-voice") {
      transport = new SmallWebRTCTransport();
      // Set custom ICE servers
      transport.iceServers = [
        { urls: ["stun:stun.miwifi.com:3478", "stun:stun.l.google.com:19302"] },
      ];
    } else {
      transport = new DailyTransport();
    }

    const newClient = new RTVIClient({
      enableCam: false,
      enableMic: conversationType === "voice-to-voice",
      transport: transport,
      params: {
        baseUrl: import.meta.env.VITE_SERVER_URL,
        endpoints: {
          connect: transport instanceof SmallWebRTCTransport ? `/offer?bot=${bot}&conversationId=${conversationId}` : "/bot/connect",
          action: "/bot/action",
        },
        requestData: {
          bot_profile:
            conversationType === "text-voice" ? "bedrock-daily" : "nova-sonic-daily",
          conversation_id: "",
          language: "Chinese",
        },
        config: [
        {
          service: "llm",
          options: [
            {
              name: "model",
              value: "Nova Pro",
            },
            // {
            //   name: "initial_messages",
            //   value: [
            //     {
            //       role: "system",
            //       content:
            //         "You are a assistant called ExampleBot. You can ask me anything. Keep responses brief and legible.",
            //     },
            //   ],
            // },
          ],
        },
      ],
      },
      // This is required for SmallWebRTCTransport
      customConnectHandler: transport instanceof SmallWebRTCTransport ? () => Promise.resolve() : undefined, 
      callbacks: {
        // Transport state changes
        onTransportStateChanged: (state) => {
          console.log(`Transport state: ${state}`);
        },

        // Connection events
        onConnected: () => {
          console.log("Connected to bot");
        },
        onDisconnected: () => {
          console.log("Disconnected from bot");
        },
        onBotReady: () => {
          console.log("Bot is ready.");
        },

        // Speech events
        onUserStartedSpeaking: () => {
          console.log("User started speaking.");
        },
        onUserStoppedSpeaking: () => {
          console.log("User stopped speaking.");
        },
        onBotStartedSpeaking: () => {
          console.log("Bot started speaking.");
        },
        onBotStoppedSpeaking: () => {
          console.log("Bot stopped speaking.");
        },

        // Transcript events
        onUserTranscript: (transcript) => {
          if (transcript.final) {
            console.log(`User transcript: ${transcript.text}`);
          }
        },
        onBotTranscript: (transcript) => {
          console.log(`Bot transcript: ${transcript.text}`);
        },

        // onRemoteAudioLevel: (level: number, participant: Participant) => {
        //   console.log(`Remote Audio: ${level}, ${participant.name}`);
        // },

        // Media tracks
        onTrackStarted: (
          track: MediaStreamTrack,
          participant?: Participant
        ) => {
          if (participant?.local) {
            // Handle local tracks (e.g., self-view)
            if (track.kind === "video") {
              //this.selfViewVideo.srcObject = new MediaStream([track]);
              //this.updateSelfViewVisibility();
            }
            return;
          }
          // Handle remote tracks (the bot)
          //this.onBotTrackStarted(track);
        },

        // Other events
        onServerMessage: (msg) => {
          console.log(`Server message: ${msg}`);
        },
      },
    });

    setClient(newClient);
  }, [conversationType, dailyEnabled]);

  useEffect(() => {
    if (!client || !conversationId) return;
    client.params.requestData = {
      ...defaultRequestData,
      ...(client.params.requestData ?? {}),
      conversation_id: conversationId,
    };
    client.params.endpoints = {
      connect: client.transport instanceof SmallWebRTCTransport ? `/offer?bot=${bot}&conversationId=${conversationId}` : "/bot/connect",
      action: "/bot/action",
    }
  }, [client, conversationId]);

  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  useLayoutEffect(() => {
    const handleScroll = () => {
      const scroller = document.scrollingElement;
      if (!scroller) return;
      const scrollBottom =
        scroller.scrollHeight - scroller.clientHeight - scroller.scrollTop;
      setShowScrollToBottom(
        scroller.scrollHeight > scroller.clientHeight && scrollBottom > 150,
      );
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    if (!client) return;
    const handleChangeLlmModel = (model: string) => {
      console.log(`Changing LLM model to: ${model}`);

      if (client.connected) {
        client.updateConfig([
          {
            service: "llm",
            options: [
              {
                name: "model",
                value: model,
              },
            ],
          },
        ]);
      } else {
        const config = client.params.config;
        if (config) {
          const llmConfig = config.find((c) => c.service === "llm");
          client.params.config = [
            ...config,
            {
              service: "llm",
              options: [
                ...(llmConfig?.options ?? []),
                {
                  name: "model",
                  value: model,
                },
              ],
            },
          ];
        } else {
          client.params.config = [
            {
              service: "llm",
              options: [
                {
                  name: "model",
                  value: model,
                },
              ],
            },
          ];
        }
      }
    };
    emitter.on("changeLlmModel", handleChangeLlmModel);
    return () => {
      emitter.off("changeLlmModel", handleChangeLlmModel);
    };
  }, [client]);

  useEffect(() => {
    if (!client) return;
    const isConnected = client.connected;
    const isConnecting =
      client.state === "authenticating" || client.state === "connecting";
    if (isConnecting || isConnected) {
      client.disconnect();
    }
  }, [client, conversationId]);

  return (
    <RTVIClientProvider client={client!}>
      <div className="flex-grow grid grid-cols-1 grid-rows-[1fr_min-content]">
        {/* Messages */}
        <div className="relative flex-grow p-4 pb-8 flex flex-col">
          {conversationType === "voice-to-voice" ? (
            <WebRTCVoiceChat />
          ) : isFetching ? (
            <div className="flex-grow flex items-center justify-center">
              <LoaderCircleIcon className="animate-spin" />
            </div>
          ) : visibleMessages.length > 0 || showMessage ? (
            <ChatMessages
              autoscroll={!showScrollToBottom}
              messages={messages}
            />
          ) : conversationType === "text-voice" ? (
            <div className="flex flex-col gap-4 items-center justify-center h-full my-auto">
              <VoiceIndicator className="shadow-md" size={72} />
              <h2 className="font-semibold text-xl text-center">
                Start chatting
              </h2>
            </div>
          ) : (
            <div className="flex flex-col gap-12 items-center justify-center h-full my-auto">
              <h2 className="font-light text-2xl text-center text-neutral-700">
                Select conversation type:
              </h2>
              <div className="grid md:grid-cols-2 gap-8 lg:gap-12 items-center justify-center">
                <Button
                  disabled={false}
                  variant="secondary-outline"
                  className="relative h-full flex flex-col border border-transparent bg-origin-border borderClip bg-cardBorder justify-between gap-2 max-w-72 lg:max-w-80 text-wrap rounded-3xl p-4 lg:p-6 shadow-mid hover:shadow-long hover:bg-cardBorderHover transition-all text-base outline outline-neutral-400/10 outline-0 hover:outline-[7px]"
                  onClick={() => setConversationType("voice-to-voice")}
                >
                  {!webrtcEnabled && (
                    <div className="bg-red-200 self-stretch absolute -top-4 left-10 right-10 z-10 rounded-full text-xs py-2 uppercase tracking-wider text-red-900">
                      <code>WebRTC</code> disabled
                    </div>
                  )}
                  <div className="flex items-center justify-center bg-sky-100 text-sky-400 rounded-full">
                    <AudioWaveformIcon className="h-20 w-20 p-4" />
                  </div>
                  <div className="flex flex-col gap-2">
                    <strong className="block mt-4 text-lg">
                      Speech to Speech
                    </strong>
                    <span className="font-light text-neutral-500">
                      Use your mic to talk with Nova Sonic using WebRTC.
                    </span>
                  </div>
                  <span className="opacity-50 inline-flex gap-1 items-center mt-4">
                    <XCircleIcon className="text-destructive" size={16} />
                    <span className="uppercase font-light text-neutral-700 text-xs tracking-wider">
                      Conversations not stored
                    </span>
                  </span>
                </Button>
                <Button
                  disabled={!webrtcEnabled}
                  variant="secondary-outline"
                  className="relative h-full flex flex-col items-center border border-transparent bg-origin-border borderClip bg-cardBorder justify-between gap-2 max-w-72 lg:max-w-80 text-wrap rounded-3xl p-4 lg:p-6 shadow-mid hover:shadow-long hover:bg-cardBorderHover transition-all text-base outline outline-neutral-400/10 outline-0 hover:outline-[7px]"
                  onClick={() => setConversationType("text-voice")}
                >
                  {!webrtcEnabled && (
                    <div className="bg-red-200 self-stretch absolute -top-4 left-10 right-10 z-10 rounded-full text-xs py-2 uppercase tracking-wider text-red-900">
                      <code>WebRTC</code> disabled
                    </div>
                  )}
                  <div className="flex items-center justify-center bg-orange-100 text-orange-400 rounded-full">
                    <PipecatLogo className="h-20 w-20 p-4" />
                  </div>
                  <div className="flex flex-col gap-2">
                    <strong className="block mt-4 text-lg">
                      Transcribe - Bedrock - Polly
                    </strong>
                    <span className="font-light text-neutral-500">
                      Use your mic, camera and keyboard to talk with Amazon Bedrock
                      using WebRTC.
                    </span>
                  </div>
                  <span className="opacity-50 inline-flex gap-1 items-center mt-4">
                    <DatabaseIcon className="text-green-400" size={16} />
                    <span className="uppercase font-light text-neutral-700 text-xs tracking-wider">
                      Conversations stored
                    </span>
                  </span>
                </Button>
              </div>
            </div>
          )}
          {showScrollToBottom && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="rounded-full fixed right-4 bottom-20 z-20"
                    onClick={() =>
                      document.scrollingElement?.scrollTo({
                        behavior: "smooth",
                        top: document.scrollingElement?.scrollHeight,
                      })
                    }
                    size="icon"
                    variant="outline"
                  >
                    <ArrowDownIcon size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent
                  align="center"
                  className="bg-popover text-popover-foreground"
                  side="left"
                >
                  Scroll to bottom
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        {/* Chat controls */}
        {conversationType === "text-voice" && (
          <div className="flex-none bg-background sticky bottom-0 w-full z-10">
            <ChatControls vision />
            {/* Prevents scroll content from showing up below chat controls */}
            <div className="h-4 bg-background w-full" />
          </div>
        )}
      </div>

      <RTVIClientAudio />
      <Settings vision={conversationType === "text-voice"} />
      <DeleteConversationModal />
    </RTVIClientProvider>
  );
}
